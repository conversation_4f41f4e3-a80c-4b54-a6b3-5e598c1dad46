# Healthcare Security - HIPAA Compliance

## 🏥 HIPAA Security Rule Overview

### Administrative Safeguards

#### Security Officer (§164.308(a)(2))
```python
class SecurityOfficer:
    """HIPAA Security Officer responsibilities implementation"""
    
    def __init__(self):
        self.responsibilities = [
            "Develop security policies and procedures",
            "Conduct security risk assessments",
            "Manage security incident response",
            "Oversee workforce training",
            "Monitor compliance"
        ]
    
    def conduct_risk_assessment(self):
        """Annual security risk assessment"""
        assessment = {
            'date': datetime.now(),
            'scope': 'All systems handling PHI',
            'methodology': 'NIST 800-30',
            'findings': [],
            'recommendations': [],
            'next_review': datetime.now() + timedelta(days=365)
        }
        return assessment
    
    def create_security_policy(self, policy_type):
        """Create HIPAA-compliant security policies"""
        policies = {
            'access_control': {
                'unique_user_identification': True,
                'automatic_logoff': True,
                'encryption_decryption': True
            },
            'audit_controls': {
                'audit_logs': True,
                'log_review': True,
                'log_retention': '6 years'
            },
            'integrity': {
                'phi_alteration_destruction': True,
                'electronic_signature': True
            }
        }
        return policies.get(policy_type)
```

#### Workforce Training (§164.308(a)(5))
```python
class HIPAATraining:
    def __init__(self):
        self.training_modules = [
            'HIPAA Privacy Rule',
            'HIPAA Security Rule',
            'Breach Notification',
            'Minimum Necessary Standard',
            'Patient Rights',
            'Incident Response'
        ]
    
    def track_training_completion(self, employee_id, module):
        """Track mandatory HIPAA training"""
        training_record = {
            'employee_id': employee_id,
            'module': module,
            'completion_date': datetime.now(),
            'score': None,
            'certificate_issued': False,
            'next_renewal': datetime.now() + timedelta(days=365)
        }
        
        # Store in secure training database
        self.store_training_record(training_record)
        
        return training_record
    
    def generate_compliance_report(self):
        """Generate training compliance report"""
        return {
            'total_employees': self.get_total_employees(),
            'trained_employees': self.get_trained_employees(),
            'compliance_rate': self.calculate_compliance_rate(),
            'overdue_training': self.get_overdue_training()
        }
```

### Physical Safeguards

#### Facility Access Controls (§164.310(a)(1))
```python
class FacilityAccessControl:
    def __init__(self):
        self.access_levels = {
            'public': ['lobby', 'waiting_room'],
            'staff': ['offices', 'break_room'],
            'clinical': ['exam_rooms', 'lab'],
            'restricted': ['server_room', 'records_storage']
        }
    
    def implement_access_controls(self):
        """Implement physical access controls"""
        controls = {
            'badge_access': {
                'required': True,
                'audit_trail': True,
                'time_restrictions': True
            },
            'visitor_management': {
                'escort_required': True,
                'visitor_log': True,
                'temporary_badges': True
            },
            'surveillance': {
                'cameras': True,
                'recording_retention': '30 days',
                'monitoring': '24/7'
            }
        }
        return controls
    
    def log_access_attempt(self, badge_id, location, timestamp):
        """Log all facility access attempts"""
        access_log = {
            'badge_id': badge_id,
            'location': location,
            'timestamp': timestamp,
            'access_granted': self.verify_access(badge_id, location),
            'tailgating_detected': self.detect_tailgating(location, timestamp)
        }
        
        # Store in secure audit log
        self.store_access_log(access_log)
        
        return access_log
```

#### Workstation Security (§164.310(b))
```python
class WorkstationSecurity:
    def __init__(self):
        self.security_requirements = {
            'screen_lock': {
                'timeout': 300,  # 5 minutes
                'password_required': True
            },
            'positioning': {
                'screen_privacy': True,
                'unauthorized_viewing': False
            },
            'software': {
                'antivirus': True,
                'firewall': True,
                'auto_updates': True
            }
        }
    
    def configure_workstation(self, workstation_id):
        """Configure workstation for HIPAA compliance"""
        config = {
            'automatic_logoff': True,
            'screen_saver_password': True,
            'usb_port_disabled': True,
            'cd_dvd_disabled': True,
            'unauthorized_software_blocked': True,
            'remote_access_controlled': True
        }
        
        # Apply configuration
        self.apply_security_config(workstation_id, config)
        
        return config
    
    def monitor_workstation_compliance(self):
        """Monitor workstation security compliance"""
        compliance_check = {
            'patch_status': self.check_patch_status(),
            'antivirus_status': self.check_antivirus_status(),
            'unauthorized_software': self.scan_unauthorized_software(),
            'security_violations': self.check_security_violations()
        }
        
        return compliance_check
```

### Technical Safeguards

#### Access Control (§164.312(a)(1))
```python
class HIPAAAccessControl:
    def __init__(self):
        self.access_matrix = {
            'physician': ['read', 'write', 'update'],
            'nurse': ['read', 'write'],
            'receptionist': ['read'],
            'billing': ['read', 'billing_update'],
            'admin': ['read', 'write', 'update', 'delete', 'admin']
        }
    
    def implement_unique_user_identification(self, user):
        """Assign unique user identification"""
        user_id = f"{user['role'][:3].upper()}{user['employee_id']:06d}"
        
        # Create user account with unique identifier
        account = {
            'user_id': user_id,
            'username': f"{user['first_name'].lower()}.{user['last_name'].lower()}",
            'role': user['role'],
            'department': user['department'],
            'created_date': datetime.now(),
            'last_login': None,
            'failed_login_attempts': 0,
            'account_locked': False
        }
        
        return account
    
    def enforce_automatic_logoff(self, session_id, timeout_minutes=15):
        """Implement automatic logoff"""
        session = self.get_session(session_id)
        
        if session:
            last_activity = session.get('last_activity')
            if last_activity:
                time_diff = datetime.now() - last_activity
                if time_diff.total_seconds() > (timeout_minutes * 60):
                    self.terminate_session(session_id)
                    self.log_automatic_logoff(session['user_id'])
                    return True
        
        return False
    
    def implement_role_based_access(self, user_role, resource):
        """Implement role-based access control"""
        allowed_actions = self.access_matrix.get(user_role, [])
        
        access_decision = {
            'user_role': user_role,
            'resource': resource,
            'allowed_actions': allowed_actions,
            'access_granted': resource in allowed_actions,
            'timestamp': datetime.now()
        }
        
        # Log access decision
        self.log_access_decision(access_decision)
        
        return access_decision['access_granted']
```

#### Audit Controls (§164.312(b))
```python
class HIPAAAuditControls:
    def __init__(self):
        self.audit_events = [
            'user_login',
            'user_logout',
            'phi_access',
            'phi_modification',
            'phi_deletion',
            'system_configuration_change',
            'security_violation'
        ]
    
    def log_phi_access(self, user_id, patient_id, action, phi_elements):
        """Log access to Protected Health Information"""
        audit_log = {
            'event_type': 'phi_access',
            'user_id': user_id,
            'patient_id': patient_id,
            'action': action,
            'phi_elements_accessed': phi_elements,
            'timestamp': datetime.now(),
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent'),
            'session_id': session.get('session_id'),
            'justification': request.json.get('access_justification')
        }
        
        # Store in tamper-evident audit log
        self.store_audit_log(audit_log)
        
        # Check for suspicious access patterns
        self.analyze_access_patterns(user_id, patient_id)
        
        return audit_log
    
    def detect_suspicious_activity(self, user_id):
        """Detect suspicious access patterns"""
        recent_access = self.get_recent_access(user_id, hours=24)
        
        suspicious_indicators = {
            'excessive_access': len(recent_access) > 100,
            'off_hours_access': self.check_off_hours_access(recent_access),
            'unusual_patient_access': self.check_unusual_patients(user_id, recent_access),
            'bulk_data_access': self.check_bulk_access(recent_access),
            'geographic_anomaly': self.check_geographic_anomaly(user_id, recent_access)
        }
        
        if any(suspicious_indicators.values()):
            self.trigger_security_alert(user_id, suspicious_indicators)
        
        return suspicious_indicators
    
    def generate_audit_report(self, start_date, end_date):
        """Generate comprehensive audit report"""
        report = {
            'period': f"{start_date} to {end_date}",
            'total_access_events': self.count_access_events(start_date, end_date),
            'unique_users': self.count_unique_users(start_date, end_date),
            'patients_accessed': self.count_patients_accessed(start_date, end_date),
            'security_violations': self.count_security_violations(start_date, end_date),
            'top_users_by_access': self.get_top_users_by_access(start_date, end_date),
            'access_by_hour': self.get_access_by_hour(start_date, end_date),
            'failed_login_attempts': self.count_failed_logins(start_date, end_date)
        }
        
        return report
```

#### Integrity (§164.312(c)(1))
```python
class PHIIntegrity:
    def __init__(self):
        self.hash_algorithm = 'sha256'
    
    def calculate_phi_hash(self, phi_data):
        """Calculate hash for PHI integrity verification"""
        phi_string = json.dumps(phi_data, sort_keys=True)
        return hashlib.sha256(phi_string.encode()).hexdigest()
    
    def verify_phi_integrity(self, phi_data, stored_hash):
        """Verify PHI has not been altered"""
        current_hash = self.calculate_phi_hash(phi_data)
        return current_hash == stored_hash
    
    def implement_digital_signatures(self, document, signer_id):
        """Implement digital signatures for PHI"""
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa, padding
        
        # Load signer's private key
        private_key = self.load_private_key(signer_id)
        
        # Create signature
        signature = private_key.sign(
            document.encode(),
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        
        signature_record = {
            'document_id': document['id'],
            'signer_id': signer_id,
            'signature': base64.b64encode(signature).decode(),
            'timestamp': datetime.now(),
            'algorithm': 'RSA-PSS-SHA256'
        }
        
        return signature_record
```

#### Transmission Security (§164.312(e)(1))
```python
class TransmissionSecurity:
    def __init__(self):
        self.encryption_standards = {
            'in_transit': 'TLS 1.3',
            'at_rest': 'AES-256',
            'key_exchange': 'ECDHE'
        }
    
    def secure_phi_transmission(self, phi_data, recipient):
        """Secure PHI transmission"""
        # Encrypt PHI data
        encrypted_data = self.encrypt_phi(phi_data)
        
        # Create secure transmission record
        transmission = {
            'transmission_id': str(uuid.uuid4()),
            'sender_id': session.get('user_id'),
            'recipient_id': recipient,
            'encrypted_data': encrypted_data,
            'encryption_method': 'AES-256-GCM',
            'timestamp': datetime.now(),
            'delivery_confirmation': False
        }
        
        # Send via secure channel
        result = self.send_secure_message(transmission)
        
        # Log transmission
        self.log_phi_transmission(transmission, result)
        
        return result
    
    def implement_end_to_end_encryption(self, message, recipient_public_key):
        """Implement end-to-end encryption for PHI"""
        from cryptography.hazmat.primitives.asymmetric import rsa, padding
        from cryptography.hazmat.primitives import hashes
        
        # Encrypt message with recipient's public key
        encrypted_message = recipient_public_key.encrypt(
            message.encode(),
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
        
        return base64.b64encode(encrypted_message).decode()
```

## 🔒 Healthcare-Specific Security Measures

### Medical Device Security
```python
class MedicalDeviceSecurity:
    def __init__(self):
        self.device_categories = {
            'class_i': 'Low risk devices',
            'class_ii': 'Moderate risk devices',
            'class_iii': 'High risk devices'
        }
    
    def secure_device_communication(self, device_id, data):
        """Secure communication with medical devices"""
        device_profile = self.get_device_profile(device_id)
        
        if device_profile['encryption_supported']:
            encrypted_data = self.encrypt_device_data(data, device_profile['key'])
        else:
            # Log unencrypted communication for legacy devices
            self.log_unencrypted_communication(device_id, data)
            encrypted_data = data
        
        return encrypted_data
    
    def validate_device_integrity(self, device_id):
        """Validate medical device integrity"""
        validation = {
            'firmware_hash': self.verify_firmware_hash(device_id),
            'certificate_valid': self.verify_device_certificate(device_id),
            'configuration_secure': self.verify_device_config(device_id),
            'network_secure': self.verify_network_security(device_id)
        }
        
        return all(validation.values())
```

### Telemedicine Security
```python
class TelemedicineSecurity:
    def __init__(self):
        self.video_encryption = 'AES-256'
        self.audio_encryption = 'SRTP'
    
    def secure_video_session(self, doctor_id, patient_id):
        """Implement secure telemedicine session"""
        session = {
            'session_id': str(uuid.uuid4()),
            'doctor_id': doctor_id,
            'patient_id': patient_id,
            'start_time': datetime.now(),
            'encryption_enabled': True,
            'recording_consent': False,
            'participants_verified': True
        }
        
        # Implement end-to-end encryption
        encryption_keys = self.generate_session_keys()
        session['encryption_keys'] = encryption_keys
        
        # Log session creation
        self.log_telemedicine_session(session)
        
        return session
    
    def verify_participant_identity(self, user_id, verification_method):
        """Verify participant identity for telemedicine"""
        verification_methods = {
            'two_factor': self.verify_2fa(user_id),
            'biometric': self.verify_biometric(user_id),
            'video_id': self.verify_video_id(user_id)
        }
        
        return verification_methods.get(verification_method, False)
```

## 📋 HIPAA Compliance Checklist

### Administrative Safeguards
- [ ] Security Officer assigned and trained
- [ ] Workforce security procedures implemented
- [ ] Information access management policies
- [ ] Security awareness training completed
- [ ] Security incident procedures documented
- [ ] Contingency plan developed and tested
- [ ] Regular security evaluations conducted

### Physical Safeguards
- [ ] Facility access controls implemented
- [ ] Workstation use restrictions in place
- [ ] Device and media controls established
- [ ] Secure disposal procedures for PHI

### Technical Safeguards
- [ ] Access control measures implemented
- [ ] Audit controls and logging active
- [ ] Integrity controls for PHI
- [ ] Person or entity authentication
- [ ] Transmission security measures

### Documentation Requirements
- [ ] Security policies and procedures documented
- [ ] Risk assessment completed annually
- [ ] Workforce training records maintained
- [ ] Incident response documentation
- [ ] Business Associate Agreements (BAAs) signed

## 🚨 Breach Notification Requirements

### Breach Assessment
```python
class BreachAssessment:
    def __init__(self):
        self.breach_threshold = 500  # individuals affected
    
    def assess_potential_breach(self, incident):
        """Assess if incident constitutes a breach"""
        assessment = {
            'incident_id': incident['id'],
            'phi_involved': incident.get('phi_involved', False),
            'individuals_affected': incident.get('affected_count', 0),
            'risk_of_harm': self.assess_risk_of_harm(incident),
            'is_breach': False,
            'notification_required': False
        }
        
        # Determine if breach notification required
        if assessment['phi_involved'] and assessment['risk_of_harm'] > 'low':
            assessment['is_breach'] = True
            assessment['notification_required'] = True
        
        return assessment
    
    def calculate_notification_timeline(self, breach_discovery_date):
        """Calculate breach notification deadlines"""
        timelines = {
            'individuals': breach_discovery_date + timedelta(days=60),
            'hhs': breach_discovery_date + timedelta(days=60),
            'media': breach_discovery_date + timedelta(days=60) if self.breach_threshold >= 500 else None
        }
        
        return timelines
```

## 📚 Next Steps

1. **Study**: Payment Security (24-payment-security.md)
2. **Implement**: HIPAA audit logging
3. **Practice**: Breach response procedures
4. **Certification**: Consider HIPAA Security Officer training
